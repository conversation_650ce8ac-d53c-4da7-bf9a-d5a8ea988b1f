#!/usr/bin/env python3
"""
清算驱动的Lead-Lag套利策略
结合VOXEL算法思想和清算数据分析的综合交易系统
"""

import pandas as pd
import numpy as np
import asyncio
import time
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
import logging
from collections import deque

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class MarketData:
    """市场数据结构"""
    timestamp: datetime
    binance_bid: float
    binance_ask: float
    bybit_bid: float
    bybit_ask: float
    binance_mid: float
    bybit_mid: float

@dataclass
class LiquidationEvent:
    """清算事件结构"""
    timestamp: datetime
    side: str  # BUY/SELL
    price: float
    quantity: float
    avg_price: float

@dataclass
class TradingSignal:
    """交易信号结构"""
    timestamp: datetime
    action: str  # BUY/SELL
    exchange: str  # binance/bybit
    price: float
    quantity: float
    confidence: float
    signal_type: str  # liquidation/lead_lag/basis_anomaly

class LiquidationLeadLagStrategy:
    """清算驱动的Lead-Lag套利策略"""
    
    def __init__(self, config: Dict):
        self.config = config
        
        # 核心参数（借鉴VOXEL算法）
        self.ttl_ms = config.get('ttl_ms', 100)  # 订单生存时间
        self.delta_pct = config.get('delta_pct', 0.2)  # 价格偏移百分比
        self.basis_window = config.get('basis_window', 60)  # 基差计算窗口
        
        # 清算相关参数
        self.liquidation_impact_window = config.get('liquidation_impact_window', 30)  # 清算影响窗口(秒)
        self.liquidation_threshold = config.get('liquidation_threshold', 1000)  # 最小清算数量阈值
        
        # Lead-Lag参数
        self.lead_lag_window = config.get('lead_lag_window', 10)  # Lead-Lag检测窗口(秒)
        self.correlation_threshold = config.get('correlation_threshold', 0.7)  # 相关性阈值
        
        # 数据存储
        self.market_data_history = deque(maxlen=1000)
        self.liquidation_events = deque(maxlen=100)
        self.basis_history = deque(maxlen=self.basis_window)
        
        # 状态变量
        self.current_basis = 0.0
        self.is_liquidation_active = False
        self.liquidation_start_time = None
        self.lead_lag_coefficient = 0.0
        
        # 风险控制
        self.max_position_size = config.get('max_position_size', 10000)
        self.max_daily_trades = config.get('max_daily_trades', 1000)
        self.daily_trade_count = 0
        
    def update_market_data(self, binance_orderbook: Dict, bybit_orderbook: Dict):
        """更新市场数据"""
        timestamp = datetime.now()
        
        # 计算中间价
        binance_mid = (binance_orderbook['bid'] + binance_orderbook['ask']) / 2
        bybit_mid = (bybit_orderbook['bid'] + bybit_orderbook['ask']) / 2
        
        market_data = MarketData(
            timestamp=timestamp,
            binance_bid=binance_orderbook['bid'],
            binance_ask=binance_orderbook['ask'],
            bybit_bid=bybit_orderbook['bid'],
            bybit_ask=bybit_orderbook['ask'],
            binance_mid=binance_mid,
            bybit_mid=bybit_mid
        )
        
        self.market_data_history.append(market_data)
        self._update_basis()
        self._update_lead_lag_coefficient()
        
    def add_liquidation_event(self, liquidation: LiquidationEvent):
        """添加清算事件"""
        self.liquidation_events.append(liquidation)
        
        # 检查是否进入清算影响期
        if liquidation.quantity >= self.liquidation_threshold:
            self.is_liquidation_active = True
            self.liquidation_start_time = liquidation.timestamp
            logger.info(f"Large liquidation detected: {liquidation.side} {liquidation.quantity} at {liquidation.price}")
    
    def _update_basis(self):
        """更新基差（借鉴VOXEL算法）"""
        if len(self.market_data_history) < 2:
            return
            
        # 计算原始基差
        recent_data = list(self.market_data_history)[-self.basis_window:]
        basis_values = [data.binance_mid - data.bybit_mid for data in recent_data]
        
        # 平滑处理
        if len(basis_values) >= 10:
            smoothed_basis = np.mean(basis_values[-10:])  # 简单移动平均
            self.basis_history.append(smoothed_basis)
            self.current_basis = smoothed_basis
    
    def _update_lead_lag_coefficient(self):
        """更新Lead-Lag系数"""
        if len(self.market_data_history) < self.lead_lag_window:
            return
            
        recent_data = list(self.market_data_history)[-self.lead_lag_window:]
        binance_prices = [data.binance_mid for data in recent_data]
        bybit_prices = [data.bybit_mid for data in recent_data]
        
        # 计算价格变化率
        binance_returns = np.diff(binance_prices) / binance_prices[:-1]
        bybit_returns = np.diff(bybit_prices) / bybit_prices[:-1]
        
        # 计算相关性（简化版本）
        if len(binance_returns) > 1:
            correlation = np.corrcoef(binance_returns, bybit_returns)[0, 1]
            self.lead_lag_coefficient = correlation if not np.isnan(correlation) else 0.0
    
    def _check_liquidation_impact_period(self) -> bool:
        """检查是否在清算影响期内"""
        if not self.is_liquidation_active or not self.liquidation_start_time:
            return False
            
        time_since_liquidation = (datetime.now() - self.liquidation_start_time).total_seconds()
        
        if time_since_liquidation > self.liquidation_impact_window:
            self.is_liquidation_active = False
            self.liquidation_start_time = None
            return False
            
        return True
    
    def calculate_fair_price(self, exchange: str) -> float:
        """计算公平价格（借鉴VOXEL算法）"""
        if len(self.market_data_history) == 0:
            return 0.0
            
        latest_data = self.market_data_history[-1]
        
        if exchange == 'bybit':
            # Bybit公平价 = Binance价格 - 基差
            fair_price = latest_data.binance_mid - self.current_basis
        else:
            # Binance公平价 = Bybit价格 + 基差
            fair_price = latest_data.bybit_mid + self.current_basis
            
        return fair_price
    
    def generate_trading_signals(self) -> List[TradingSignal]:
        """生成交易信号"""
        signals = []
        
        if len(self.market_data_history) < 2:
            return signals
            
        latest_data = self.market_data_history[-1]
        
        # 1. 清算驱动信号
        if self._check_liquidation_impact_period():
            liquidation_signals = self._generate_liquidation_signals(latest_data)
            signals.extend(liquidation_signals)
        
        # 2. Lead-Lag信号
        if abs(self.lead_lag_coefficient) > self.correlation_threshold:
            lead_lag_signals = self._generate_lead_lag_signals(latest_data)
            signals.extend(lead_lag_signals)
        
        # 3. 基差异常信号
        basis_signals = self._generate_basis_anomaly_signals(latest_data)
        signals.extend(basis_signals)
        
        return signals
    
    def _generate_liquidation_signals(self, market_data: MarketData) -> List[TradingSignal]:
        """生成清算驱动的交易信号"""
        signals = []
        
        if not self.liquidation_events:
            return signals
            
        latest_liquidation = self.liquidation_events[-1]
        
        # 根据清算方向和我们的分析结果生成信号
        if latest_liquidation.side == 'SELL':
            # SELL清算通常导致价格下跌，在Bybit做空，Binance做多
            bybit_fair_price = self.calculate_fair_price('bybit')
            order_price = bybit_fair_price * (1 + self.delta_pct / 100)
            
            if order_price < market_data.bybit_ask:  # 可以立即成交
                signals.append(TradingSignal(
                    timestamp=market_data.timestamp,
                    action='SELL',
                    exchange='bybit',
                    price=order_price,
                    quantity=min(latest_liquidation.quantity * 0.1, self.max_position_size),
                    confidence=0.8,
                    signal_type='liquidation'
                ))
                
        elif latest_liquidation.side == 'BUY':
            # BUY清算通常导致价格上涨
            bybit_fair_price = self.calculate_fair_price('bybit')
            order_price = bybit_fair_price * (1 - self.delta_pct / 100)
            
            if order_price > market_data.bybit_bid:  # 可以立即成交
                signals.append(TradingSignal(
                    timestamp=market_data.timestamp,
                    action='BUY',
                    exchange='bybit',
                    price=order_price,
                    quantity=min(latest_liquidation.quantity * 0.1, self.max_position_size),
                    confidence=0.8,
                    signal_type='liquidation'
                ))
        
        return signals
    
    def _generate_lead_lag_signals(self, market_data: MarketData) -> List[TradingSignal]:
        """生成Lead-Lag信号"""
        signals = []
        
        if len(self.market_data_history) < 3:
            return signals
            
        # 检测Binance价格变化趋势
        prev_data = self.market_data_history[-2]
        price_change_pct = (market_data.binance_mid - prev_data.binance_mid) / prev_data.binance_mid
        
        # 如果Binance价格有显著变化，预期Bybit将跟随
        if abs(price_change_pct) > 0.001:  # 0.1%以上的变化
            if price_change_pct > 0:  # Binance上涨，预期Bybit跟随
                bybit_target_price = market_data.bybit_mid * (1 + price_change_pct * 0.8)
                if bybit_target_price > market_data.bybit_ask:
                    signals.append(TradingSignal(
                        timestamp=market_data.timestamp,
                        action='BUY',
                        exchange='bybit',
                        price=market_data.bybit_ask,
                        quantity=self.max_position_size * 0.5,
                        confidence=abs(self.lead_lag_coefficient),
                        signal_type='lead_lag'
                    ))
            else:  # Binance下跌，预期Bybit跟随
                bybit_target_price = market_data.bybit_mid * (1 + price_change_pct * 0.8)
                if bybit_target_price < market_data.bybit_bid:
                    signals.append(TradingSignal(
                        timestamp=market_data.timestamp,
                        action='SELL',
                        exchange='bybit',
                        price=market_data.bybit_bid,
                        quantity=self.max_position_size * 0.5,
                        confidence=abs(self.lead_lag_coefficient),
                        signal_type='lead_lag'
                    ))
        
        return signals
    
    def _generate_basis_anomaly_signals(self, market_data: MarketData) -> List[TradingSignal]:
        """生成基差异常信号"""
        signals = []
        
        if len(self.basis_history) < 10:
            return signals
            
        current_spread = market_data.binance_mid - market_data.bybit_mid
        basis_std = np.std(list(self.basis_history))
        basis_mean = np.mean(list(self.basis_history))
        
        # Z-score计算
        if basis_std > 0:
            z_score = (current_spread - basis_mean) / basis_std
            
            # 异常基差信号
            if abs(z_score) > 2:  # 2个标准差以外
                if z_score > 2:  # Binance相对过高
                    signals.append(TradingSignal(
                        timestamp=market_data.timestamp,
                        action='SELL',
                        exchange='binance',
                        price=market_data.binance_bid,
                        quantity=self.max_position_size * 0.3,
                        confidence=min(abs(z_score) / 3, 1.0),
                        signal_type='basis_anomaly'
                    ))
                    signals.append(TradingSignal(
                        timestamp=market_data.timestamp,
                        action='BUY',
                        exchange='bybit',
                        price=market_data.bybit_ask,
                        quantity=self.max_position_size * 0.3,
                        confidence=min(abs(z_score) / 3, 1.0),
                        signal_type='basis_anomaly'
                    ))
                elif z_score < -2:  # Bybit相对过高
                    signals.append(TradingSignal(
                        timestamp=market_data.timestamp,
                        action='BUY',
                        exchange='binance',
                        price=market_data.binance_ask,
                        quantity=self.max_position_size * 0.3,
                        confidence=min(abs(z_score) / 3, 1.0),
                        signal_type='basis_anomaly'
                    ))
                    signals.append(TradingSignal(
                        timestamp=market_data.timestamp,
                        action='SELL',
                        exchange='bybit',
                        price=market_data.bybit_bid,
                        quantity=self.max_position_size * 0.3,
                        confidence=min(abs(z_score) / 3, 1.0),
                        signal_type='basis_anomaly'
                    ))
        
        return signals
    
    def execute_signals(self, signals: List[TradingSignal]):
        """执行交易信号（模拟）"""
        for signal in signals:
            if self.daily_trade_count >= self.max_daily_trades:
                logger.warning("Daily trade limit reached")
                break
                
            logger.info(f"Executing signal: {signal.action} {signal.quantity} on {signal.exchange} "
                       f"at {signal.price} (confidence: {signal.confidence:.2f}, type: {signal.signal_type})")
            
            # 这里应该调用实际的交易API
            # self._place_order(signal)
            
            self.daily_trade_count += 1
    
    def run_strategy_cycle(self):
        """运行一个策略周期（模拟VOXEL的100ms周期）"""
        try:
            # 1. 生成交易信号
            signals = self.generate_trading_signals()
            
            # 2. 执行信号
            if signals:
                self.execute_signals(signals)
            
            # 3. 风险检查
            self._risk_check()
            
        except Exception as e:
            logger.error(f"Strategy cycle error: {e}")
    
    def _risk_check(self):
        """风险检查"""
        # 这里实现各种风险控制逻辑
        pass
    
    def get_strategy_stats(self) -> Dict:
        """获取策略统计信息"""
        return {
            'current_basis': self.current_basis,
            'lead_lag_coefficient': self.lead_lag_coefficient,
            'is_liquidation_active': self.is_liquidation_active,
            'daily_trade_count': self.daily_trade_count,
            'market_data_points': len(self.market_data_history),
            'liquidation_events': len(self.liquidation_events)
        }

# 使用示例
if __name__ == "__main__":
    config = {
        'ttl_ms': 100,
        'delta_pct': 0.2,
        'basis_window': 60,
        'liquidation_impact_window': 30,
        'liquidation_threshold': 1000,
        'lead_lag_window': 10,
        'correlation_threshold': 0.7,
        'max_position_size': 10000,
        'max_daily_trades': 1000
    }
    
    strategy = LiquidationLeadLagStrategy(config)
    
    print("🚀 清算驱动的Lead-Lag套利策略已启动")
    print("策略特点:")
    print("- 结合VOXEL算法的Lead-Lag机制")
    print("- 利用清算事件作为信号放大器")
    print("- 100ms高频执行周期")
    print("- 多层次风险控制")
    print("- 实时基差和相关性监控")
